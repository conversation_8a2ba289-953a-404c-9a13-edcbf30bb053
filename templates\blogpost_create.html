{% extends 'base.html' %}

{% block content %}
{# Main container with responsive grid #}
<div class="container">
  <div class="row justify-content-center">
    {# Responsive column layout - lg: 4 cols, md: 6 cols, sm: 12 cols #}
    <div class="col-md-6 col-md-6 col-sm-12">
      {# Form header #}
      <h2 class="account-heading">Create a New Blog Post</h2>

      {# Post creation form - Note: enctype required for file uploads #}
      <form method="post" enctype="multipart/form-data" id="blogPostForm">
        {% csrf_token %}

        {# Blog Title Field #}
        <div class="mb-3">
          {{ form.blog_title.label_tag }}
          {{ form.blog_title }}
          {% if form.blog_title.errors %}
          <ul>
            {% for error in form.blog_title.errors %}
            <li class="text-danger">{{ error }}</li>
            {% endfor %}
          </ul>
          {% endif %}
        </div>

        {# Content Field #}
        <div class="mb-3">
          {{ form.content.label_tag }}
          {{ form.content }}
          {% if form.content.errors %}
          <ul>
            {% for error in form.content.errors %}
            <li class="text-danger">{{ error }}</li>
            {% endfor %}
          </ul>
          {% endif %}
        </div>

        {# Excerpt Field #}
        <div class="mb-3">
          {{ form.excerpt.label_tag }}
          {{ form.excerpt }}
          {% if form.excerpt.errors %}
          <ul>
            {% for error in form.excerpt.errors %}
            <li class="text-danger">{{ error }}</li>
            {% endfor %}
          </ul>
          {% endif %}
        </div>

        {# Featured Image Field #}
        <div class="mb-3">
          {{ form.featured_image.label_tag }}
          {{ form.featured_image }}
          {% if form.featured_image.errors %}
          <ul>
            {% for error in form.featured_image.errors %}
            <li class="text-danger">{{ error }}</li>
            {% endfor %}
          </ul>
          {% endif %}
        </div>

        {# Media Category Field #}
        <div class="mb-3">
          {{ form.media_category.label_tag }}
          {{ form.media_category }}
          {% if form.media_category.errors %}
          <ul>
            {% for error in form.media_category.errors %}
            <li class="text-danger">{{ error }}</li>
            {% endfor %}
          </ul>
          {% endif %}
        </div>

        {# Release Year Field #}
        <div class="mb-3">
          {{ form.release_year.label_tag }}
          {{ form.release_year }}
          {% if form.release_year.errors %}
          <ul>
            {% for error in form.release_year.errors %}
            <li class="text-danger">{{ error }}</li>
            {% endfor %}
          </ul>
          {% endif %}
        </div>

        {# Media Link Field #}
        <div class="mb-3">
          {{ form.media_link.label_tag }}
          {{ form.media_link }}
          {% if form.media_link.errors %}
          <ul>
            {% for error in form.media_link.errors %}
            <li class="text-danger">{{ error }}</li>
            {% endfor %}
          </ul>
          {% endif %}
        </div>

        <button type="submit" class="btn btn-primary">Create Post</button>
      </form>
    </div>
  </div>
</div>
{% endblock content %}

{% block scripts %}
{{ form.media }}

<script>
$(document).ready(function() {
    // Form validation for blog post creation
    $('#blogPostForm').on('submit', function(e) {
        // Clear any existing warning messages
        $('.validation-warning').remove();

        let hasErrors = false;

        // Define required fields to validate
        const requiredFields = [
            {
                field: $('#id_blog_title'),
                name: 'Blog Title'
            },
            {
                field: $('#id_content'),
                name: 'Content'
            },
            {
                field: $('#id_excerpt'),
                name: 'Excerpt'
            },
            {
                field: $('#id_media_category'),
                name: 'Media Category'
            },
            {
                field: $('#id_release_year'),
                name: 'Release Year'
            },
            {
                field: $('#id_media_link'),
                name: 'Media Link'
            }
        ];

        // Check each required field
        requiredFields.forEach(function(fieldObj) {
            const field = fieldObj.field;
            const fieldName = fieldObj.name;
            let value = field.val();

            // Special handling for different field types
            if (field.is('select')) {
                // For select fields, check if a valid option is selected
                if (!value || value === '' || value === 'Select an option') {
                    showWarning(field, fieldName + ' is required. Please select an option.');
                    hasErrors = true;
                }
            } else if (field.attr('type') === 'url') {
                // For URL fields, check if it's empty or just the default placeholder
                if (!value || value.trim() === '' || value.trim() === 'http://www.') {
                    showWarning(field, fieldName + ' is required. Please enter a valid URL.');
                    hasErrors = true;
                }
            } else {
                // For text fields and textareas
                if (!value || value.trim() === '') {
                    showWarning(field, fieldName + ' is required. Please fill in this field.');
                    hasErrors = true;
                }
            }
        });

        // Prevent form submission if there are errors
        if (hasErrors) {
            e.preventDefault();

            // Scroll to the first error
            const firstError = $('.validation-warning').first();
            if (firstError.length) {
                $('html, body').animate({
                    scrollTop: firstError.offset().top - 100
                }, 500);
            }

            return false;
        }
    });

    // Function to display warning messages
    function showWarning(field, message) {
        // Create warning element
        const warningDiv = $('<div class="validation-warning alert alert-warning mt-2" role="alert">')
            .html('<i class="fas fa-exclamation-triangle me-2"></i>' + message)
            .css({
                'color': '#dd1c1a',
                'border-color': '#dd1c1a',
                'background-color': '#ffeaea',
                'font-size': '0.875rem',
                'padding': '0.5rem 0.75rem'
            });

        // Insert warning after the field's parent div
        field.closest('.mb-3').append(warningDiv);

        // Add red border to the field
        field.css('border-color', '#dd1c1a');

        // Remove warning and red border when user starts typing/selecting
        field.on('input change', function() {
            $(this).css('border-color', '');
            $(this).closest('.mb-3').find('.validation-warning').fadeOut(300, function() {
                $(this).remove();
            });
        });
    }
});
</script>
{% endblock scripts %}
