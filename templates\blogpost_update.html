{# Template for editing blog posts #}
{% extends 'base.html' %}

{% load crispy_forms_tags %}

{% block content %}
    <div class="container col-md-8 col-sm-12">
        <div class="masthead">
            <div class="container card-body">
                <div class="row g-0">
                    <div class="col-md-12 masthead-text">
                        <h1 class="post-title">Edit & Update your Blog Post</h1>

                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col card mb-4">
                <div class="card-body">
                    <form method="post" enctype="multipart/form-data" id="blogUpdateForm">
                        {% csrf_token %}

                        {# Blog Title Field #}
                        <div class="mb-3">
                            {{ form.blog_title.label_tag }}
                            {{ form.blog_title }}
                            {% if form.blog_title.errors %}
                                <ul>
                                    {% for error in form.blog_title.errors %}
                                        <li class="text-danger">{{ error }}</li>
                                    {% endfor %}
                                </ul>
                            {% endif %}
                        </div>

                        {# Content Field #}
                        <div class="mb-3">
                            {{ form.content.label_tag }}
                            {{ form.content }}
                            {% if form.content.errors %}
                                <ul>
                                    {% for error in form.content.errors %}
                                        <li class="text-danger">{{ error }}</li>
                                    {% endfor %}
                                </ul>
                            {% endif %}
                        </div>

                        {# Excerpt Field #}
                        <div class="mb-3">
                            {{ form.excerpt.label_tag }}
                            {{ form.excerpt }}
                            {% if form.excerpt.errors %}
                                <ul>
                                    {% for error in form.excerpt.errors %}
                                        <li class="text-danger">{{ error }}</li>
                                    {% endfor %}
                                </ul>
                            {% endif %}
                        </div>

                        {# Featured Image Field #}
                        <div class="mb-3">
                            {{ form.featured_image.label_tag }}
                            {{ form.featured_image }}
                            {% if form.featured_image.errors %}
                                <ul>
                                    {% for error in form.featured_image.errors %}
                                        <li class="text-danger">{{ error }}</li>
                                    {% endfor %}
                                </ul>
                            {% endif %}
                        </div>

                        {# Media Category Field #}
                        <div class="mb-3">
                            {{ form.media_category.label_tag }}
                            {{ form.media_category }}
                            {% if form.media_category.errors %}
                                <ul>
                                    {% for error in form.media_category.errors %}
                                        <li class="text-danger">{{ error }}</li>
                                    {% endfor %}
                                </ul>
                            {% endif %}
                        </div>

                        {# Release Year Field #}
                        <div class="mb-3">
                            {{ form.release_year.label_tag }}
                            {{ form.release_year }}
                            {% if form.release_year.errors %}
                                <ul>
                                    {% for error in form.release_year.errors %}
                                        <li class="text-danger">{{ error }}</li>
                                    {% endfor %}
                                </ul>
                            {% endif %}
                        </div>

                        {# Media Link Field #}
                        <div class="mb-3">
                            {{ form.media_link.label_tag }}
                            {{ form.media_link }}
                            {% if form.media_link.errors %}
                                <ul>
                                    {% for error in form.media_link.errors %}
                                        <li class="text-danger">{{ error }}</li>
                                    {% endfor %}
                                </ul>
                            {% endif %}
                        </div>

                        <div class="text-center mt-4">
                            <button type="submit" class="btn btn-primary btn-lg">Save changes</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
{% endblock content %}

{% block scripts %}
{{ form.media }}

<script>
$(document).ready(function() {
    // Form validation for blog post update
    $('#blogUpdateForm').on('submit', function(e) {
        // Clear any existing warning messages
        $('.validation-warning').remove();

        let hasErrors = false;

        // Define required fields to validate
        const requiredFields = [
            {
                field: $('#id_blog_title'),
                name: 'Blog Title'
            },
            {
                field: $('#id_content'),
                name: 'Content'
            },
            {
                field: $('#id_excerpt'),
                name: 'Excerpt'
            },
            {
                field: $('#id_media_category'),
                name: 'Media Category'
            },
            {
                field: $('#id_release_year'),
                name: 'Release Year'
            },
            {
                field: $('#id_media_link'),
                name: 'Media Link'
            }
        ];

        // Check each required field
        requiredFields.forEach(function(fieldObj) {
            const field = fieldObj.field;
            const fieldName = fieldObj.name;
            let value = field.val();

            // Special handling for different field types
            if (field.is('select')) {
                // For select fields, check if a valid option is selected
                if (!value || value === '' || value === 'Select an option') {
                    showWarning(field, fieldName + ' is required. Please select an option.');
                    hasErrors = true;
                }
            } else if (field.attr('type') === 'url') {
                // For URL fields, check if it's empty or just the default placeholder
                if (!value || value.trim() === '' || value.trim() === 'http://www.') {
                    showWarning(field, fieldName + ' is required. Please enter a valid URL.');
                    hasErrors = true;
                }
            } else if (field.attr('type') === 'number') {
                // For number fields, check if it's empty or invalid
                if (!value || value.trim() === '' || isNaN(value)) {
                    showWarning(field, fieldName + ' is required. Please enter a valid year.');
                    hasErrors = true;
                }
            } else {
                // For text fields and textareas (including Summernote)
                if (!value || value.trim() === '') {
                    showWarning(field, fieldName + ' is required. Please fill in this field.');
                    hasErrors = true;
                }
            }
        });

        // Prevent form submission if there are errors
        if (hasErrors) {
            e.preventDefault();

            // Show general error message at the top
            showGeneralError();

            // Scroll to the first error
            const firstError = $('.validation-warning').first();
            if (firstError.length) {
                $('html, body').animate({
                    scrollTop: firstError.offset().top - 100
                }, 500);
            }

            return false;
        }
    });

    // Function to display warning messages for individual fields
    function showWarning(field, message) {
        // Create warning element
        const warningDiv = $('<div class="validation-warning alert alert-warning mt-2" role="alert">')
            .html('<i class="fas fa-exclamation-triangle me-2"></i>' + message)
            .css({
                'color': '#dd1c1a',
                'border-color': '#dd1c1a',
                'background-color': '#ffeaea',
                'font-size': '0.875rem',
                'padding': '0.5rem 0.75rem'
            });

        // Insert warning after the field's parent div
        field.closest('.mb-3').append(warningDiv);

        // Add red border to the field
        field.css('border-color', '#dd1c1a');

        // Remove warning and red border when user starts typing/selecting
        field.on('input change', function() {
            $(this).css('border-color', '');
            $(this).closest('.mb-3').find('.validation-warning').fadeOut(300, function() {
                $(this).remove();
            });
            // Remove general error message when user starts fixing issues
            $('.general-error-message').fadeOut(300, function() {
                $(this).remove();
            });
        });
    }

    // Function to display general error message
    function showGeneralError() {
        // Remove any existing general error message
        $('.general-error-message').remove();

        // Create general error message
        const generalErrorDiv = $('<div class="general-error-message alert alert-danger mb-4" role="alert">')
            .html('<i class="fas fa-exclamation-circle me-2"></i><strong>Please complete all required fields before saving changes.</strong> All input fields must be filled in.')
            .css({
                'color': '#dd1c1a',
                'border-color': '#dd1c1a',
                'background-color': '#ffeaea',
                'font-weight': 'bold'
            });

        // Insert at the top of the form
        $('#blogUpdateForm').prepend(generalErrorDiv);
    }
});
</script>
{% endblock scripts %}